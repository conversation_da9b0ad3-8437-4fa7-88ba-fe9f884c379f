package com.mall.project.task.executor;

import com.mall.project.service.areaAuthorize.AreaAuthorizeService;
import com.mall.project.service.cQuantifyEvolve.CQuantifyEvolveService;
import com.mall.project.service.ccreditEvolve.CcreditEvolveService;
import com.mall.project.service.cooperateEnterprise.CooperateEnterpriseService;
import com.mall.project.service.creditEvolve.CreditEvolveService;
import com.mall.project.service.functionDatas.FunctionDatasService;
import com.mall.project.service.mallBUsers.GetMallBUsersService;
import com.mall.project.service.quantifyCount.QuantifyCountService;
import com.mall.project.service.quantifyEvolve.QuantifyEvolveService;
import com.mall.project.service.quantizationValue.QuantizationValueService;
import com.mall.project.service.statusSet.StatusSetService;
import com.mall.project.service.sysDataStatistics.SysDataStatisticsService;
import com.mall.project.service.systemDatas.SystemDatasService;
import com.mall.project.service.systemInfo.SystemInfoService;
import com.mall.project.service.writeOffData.WriteOffDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 数据同步任务执行器
 * 负责执行各种数据同步相关的定时任务
 */
@Slf4j
@Component
public class DataSyncTaskExecutor {
    private static final DateTimeFormatter MONTH_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM");

    @Autowired
    private GetMallBUsersService getMallBUsersService;

    @Autowired
    private CooperateEnterpriseService cooperateEnterpriseService;

    @Autowired
    private QuantifyCountService quantifyCountService;

    @Autowired
    private AreaAuthorizeService areaAuthorizeService;

    @Autowired
    private FunctionDatasService functionDatasService;

    @Autowired
    private SystemInfoService systemInfoService;

    @Autowired
    private SystemDatasService systemDatasService;

    @Autowired
    private QuantizationValueService quantizationValueService;

    @Autowired
    private CreditEvolveService creditEvolveService;

    @Autowired
    private WriteOffDataService writeOffDataService;

    @Autowired
    private SysDataStatisticsService sysDataStatisticsService;

    @Autowired
    private QuantifyEvolveService quantifyEvolveService;

    @Autowired
    private StatusSetService statusSetService;

    @Autowired
    private CQuantifyEvolveService cQuantifyEvolveService;

    @Autowired
    private CcreditEvolveService ccreditEvolveService;

    /**
     * 执行数据同步任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeDataSyncTasks() {
        log.info("开始执行数据同步任务");

        try {
            log.info("正在同步用户数据...");
            getMallBUsersService.getMallBUsers();

            log.info("正在从mallB系统读取 超级管理员手机号码...");
            getMallBUsersService.getSuperAdminPhone();

            log.info("正在同步交易数据...");
            cooperateEnterpriseService.getTradeDataFromMallB();

            log.info("正在从mallB系统读取核销数据...");
            writeOffDataService.getWriteOffDataFromMallB();

            log.info("正在从mallB系统获取 Admin量化值进化量数据...");
            quantifyEvolveService.getAdminQuantifyEvolveFromMallB();

            log.info("正在从mallB系统获取 Admin量化进化量数据...");
            creditEvolveService.getAdminCreditEvolveFromMallB();

            log.info("正在从mallB系统获取用户类型为B的量化值进化量数据...");
            quantifyEvolveService.getQuantifyEvolveFromMallB();

            log.info("正在从mallB系统获取信用值进化量数据...");
            creditEvolveService.getCreditEvolveFromMallB();

            log.info("正在从mallB系统读取 促销金数据...");
            writeOffDataService.getPromotionDataFromMallB();

            log.info("正在从mallB系统读取 核销金数据...");
            writeOffDataService.getWriteOffGoldFromMallB();

            log.info("正在计算企业交易数据...");
            cooperateEnterpriseService.updateEnterpriseTradeData();

            log.info("正在计算量化数计算...");
            quantifyCountService.updateQuantifyCount();

            log.info("正在计算admin的量化数...");
            quantifyCountService.adminDailyQuantity();

            log.info("正在计算功能数据量化数...");
            functionDatasService.updateQuantifyCount();

            log.info("更新累计量化数...");
            quantifyCountService.updateTotalQuantifyCount();

            log.info("正在计算企业交易数据...");
            sysDataStatisticsService.statisticsData("query", "0", null);

            log.info("正在计算企业量化数据...");
            sysDataStatisticsService.statisticsData("query", "1", null);

            log.info("正在计算量化率...");
            String currentMonth = LocalDate.now().format(MONTH_FORMAT);
            cooperateEnterpriseService.quantizationRate(currentMonth);

            log.info("正在计算量化值...");
            quantizationValueService.updateQuantizationValue();

            log.info("正在计算C量化值进化量...");
            cQuantifyEvolveService.updateCQuantifyEvolve();

            log.info("正在计算信用值进化量...");
            ccreditEvolveService.updateCreditEvolve();

            log.info("正在计算C用户状态异常的补贴金...");
            areaAuthorizeService.updateCSubsidy();

            log.info("正在计算 状态数据量化值和补贴金...");
            statusSetService.updateQuantifyAndSubsidy();

            log.info("正在执行每日Admin量化值计算...");
            quantizationValueService.adminDailyQuantifyValue("");

            log.info("正在计算系统数据...");
            systemDatasService.updateSystemDatas("");

            log.info("正在计算系统信息...");
            systemInfoService.updateSystemInfo();

            log.info("正在删除授权表中过期的授权信息...");
            areaAuthorizeService.deleteExpiredAuthorize();

            log.info("数据同步任务执行完成");

        } catch (Exception e) {
            log.error("数据同步任务执行过程中发生错误", e);
            throw e;
        }
    }
}