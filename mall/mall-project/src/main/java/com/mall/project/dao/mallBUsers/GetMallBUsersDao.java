package com.mall.project.dao.mallBUsers;

import com.mall.project.dto.mallBUsers.MallBUsers;
import com.mall.project.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 从mallB系统读取用户数据保存
 */
@Repository
@Slf4j
public class GetMallBUsersDao {


    @Autowired
    private JdbcTemplate jdbcTemplate;


    /**
     * 获取所有mallB系统的用户数据
     * @return 用户数据列表
     */
    public List<Map<String, Object>> getMallBUsers(String phone,String businessLicense, String status, String userType, int limit, int offset) {
        List<Object> params = new ArrayList<>();
        try{
            String sql = "SELECT\n" +
                    "  id,\n" +
                    "  phone,\n" +
                    "  parent_username,\n" +
                    "CASE\n" +
                    "  STATUS \n" +
                    "    WHEN 0 THEN\n" +
                    "    '正常' \n" +
                    "    WHEN 1 THEN\n" +
                    "    '禁用' \n" +
                    "    WHEN 2 THEN\n" +
                    "    '删除' \n" +
                    "    WHEN 3 THEN\n" +
                    "    '失效' \n" +
                    "    WHEN 4 THEN\n" +
                    "    '睡眠' \n" +
                    "    WHEN 5 THEN\n" +
                    "    '无效' ELSE STATUS \n" +
                    "  END as status,\n" +
                    "CASE\n" +
                    "    flag \n" +
                    "    WHEN 0 THEN\n" +
                    "    '未达标' \n" +
                    "    WHEN 1 THEN\n" +
                    "    '达标一' \n" +
                    "    WHEN 2 THEN\n" +
                    "    '达标二' ELSE '' \n" +
                    "  END as flag,\n" +
                    "  deduction_money_limit,\n" +
                    "  create_time,\n" +
                    "CASE\n" +
                    "    jurisdiction \n" +
                    "    WHEN 1 THEN\n" +
                    "    '权限一' \n" +
                    "    WHEN 2 THEN\n" +
                    "    '权限二' \n" +
                    "    WHEN 3 THEN\n" +
                    "    '权限三' ELSE jurisdiction \n" +
                    "  END as jurisdiction,\n" +
                    "  username,\n" +
                    "  fans,\n" +
                    "  address,\n" +
                    "  login_address, \n" +
                    "  business_license \n" +
                    "FROM\n" +
                    "  mall_b_users WHERE 1=1";
            if (phone != null && !phone.trim().isEmpty()) {
                sql += " AND phone LIKE ? ";
                params.add("%" + phone + "%");
            }
            if (businessLicense != null && !businessLicense.trim().isEmpty()) {
                sql += " AND business_license LIKE ? ";
                params.add("%" + businessLicense + "%");
            }
            if (status != null && !status.trim().isEmpty()) {
                sql += " AND status = ? ";
                params.add(status);
            }
            if (userType != null && !userType.trim().isEmpty()) {
                sql += " AND user_type = ? ";
                params.add(userType);
            }
            sql += " ORDER BY create_time DESC LIMIT " + limit + " OFFSET " + offset;
            if(params.isEmpty()){
                return jdbcTemplate.queryForList(sql);
            }else{
                return jdbcTemplate.queryForList(sql, params.toArray());
            }
        }catch (Exception e){
            // 当数据库中没有数据时，返回空列表，让Service层处理
            log.error("查询mallB用户数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取所有mallB系统的用户数据 总条数
     */
    public int getMallBUsersCount(String phone,String businessLicense, String status, String userType) {
        try{
            String sql = "SELECT COUNT(*) FROM mall_b_users WHERE 1=1";
            if (phone != null && !phone.trim().isEmpty()) {
                sql += " AND phone like ?";
                return jdbcTemplate.queryForObject(sql, Integer.class, "%" + phone + "%");
            }
            if (businessLicense != null && !businessLicense.trim().isEmpty()) {
                sql += " AND business_license like ?";
                return jdbcTemplate.queryForObject(sql, Integer.class, "%" + businessLicense + "%");
            }
            if (status != null && !status.trim().isEmpty()) {
                sql += " AND status = ?";
                return jdbcTemplate.queryForObject(sql, Integer.class, status);
            }
            if (userType != null && !userType.trim().isEmpty()) {
                sql += " AND user_type = ?";
                return jdbcTemplate.queryForObject(sql, Integer.class, userType);
            }
            return jdbcTemplate.queryForObject(sql, Integer.class);
        }catch (Exception e){
            // 当数据库中没有数据时，返回0，让Service层处理
            log.error("查询mallB用户数据总条数失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 删除所有mallB系统的用户数据
     */
    public void deleteMallBUsers(){
        // 删除 mall_b_users 表中的所有数据
        String sql = "DELETE FROM mall_b_users";
        jdbcTemplate.update(sql);
        // 删除 mall_b_users_count 表中当天的数据
        sql = "DELETE FROM mall_b_users_count WHERE DATE(update_time) = CURDATE()";
        jdbcTemplate.update(sql);
        // 删除 quantization_value 表中当天的数据
        sql = "DELETE FROM quantization_value WHERE DATE(update_date) = CURDATE()";
        jdbcTemplate.update(sql);
        // 删除 write_off_data 表中当天的数据
        sql = "DELETE FROM write_off_data WHERE DATE(update_date) = CURDATE()";
        jdbcTemplate.update(sql);
        // 删除 quantify_evolve 表中当天的数据
        sql = "DELETE FROM quantify_evolve WHERE DATE(update_date) = CURDATE()";
        jdbcTemplate.update(sql);
        // 删除 credit_evolve 表中当天的数据
        sql = "DELETE FROM credit_evolve WHERE DATE(update_date) = CURDATE()";
        jdbcTemplate.update(sql);
        // 删除 cquantify_evolve 表中当天的数据
        sql = "DELETE FROM cquantify_evolve WHERE DATE(update_date) = CURDATE()";
        jdbcTemplate.update(sql);
        // 删除 ccredit_evolve 表中当天的数据
        sql = "DELETE FROM ccredit_evolve WHERE DATE(update_date) = CURDATE()";
        jdbcTemplate.update(sql);
        // 删除 CSubsidy_result 表中当天的数据
        sql = "DELETE FROM CSubsidy_result WHERE DATE(update_time) = CURDATE()";
        jdbcTemplate.update(sql);
        // 删除 quantization_rate 表中当天的数据
        sql = "DELETE FROM quantization_rate WHERE DATE(create_time) = CURDATE()";
        jdbcTemplate.update(sql);
        // 删除 enterprise_product_data 表中当天的数据
        sql = "DELETE FROM enterprise_product_data WHERE DATE(update_time) = CURDATE()";
        jdbcTemplate.update(sql);
        // 删除 enterprise_trade_stats 表中当天的数据
        sql = "DELETE FROM enterprise_trade_stats WHERE DATE(update_time) = CURDATE()";
        jdbcTemplate.update(sql);
        // 删除 quantify_subsidy_total 表中当天的数据
        sql = "DELETE FROM quantify_subsidy_total WHERE DATE(update_date) = CURDATE()";
        jdbcTemplate.update(sql);
    }


    /**
     * 保存mallB系统的用户数据
     */
    @Transactional
    public void saveMallBUsers(MallBUsers mallBUsers){
        try {
            String insertSql = "INSERT INTO mall_b_users(id, username, parent_id,parent_username,user_type, chain_type, status, deduction_money_limit, business_name, phone, fans, address, town_code, jurisdiction, flag, login_address,business_license,create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            jdbcTemplate.update(connection -> {
                var ps = connection.prepareStatement(insertSql);
                try {
                    ps.setString(1, mallBUsers.getId());
                    ps.setString(2, mallBUsers.getUserName());
                    ps.setString(3, mallBUsers.getParentId());
                    ps.setString(4, mallBUsers.getParentUserName());
                    ps.setString(5, mallBUsers.getUserType());
                    ps.setString(6, mallBUsers.getChainType());
                    ps.setString(7, mallBUsers.getStatus());
                    ps.setString(8, mallBUsers.getDeductionMoneyLimit());
                    ps.setString(9, mallBUsers.getBusinessName());
                    ps.setString(10, mallBUsers.getPhone());
                    ps.setInt(11, mallBUsers.getFans());
                    ps.setString(12, mallBUsers.getAddress());
                    ps.setString(13, mallBUsers.getTownCode());
                    ps.setString(14, mallBUsers.getJurisdiction());
                    ps.setString(15, mallBUsers.getFlag());
                    ps.setString(16, mallBUsers.getLoginAddress());
                    ps.setString(17, mallBUsers.getSocialCreditCode());
                    ps.setString(18, mallBUsers.getCreateTime());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                return ps;
            });
            // 检查该phone在当天是否已经有数据
            String checkSql = "SELECT EXISTS(SELECT 1 FROM mall_b_users_count WHERE phone = ? AND update_time = CURDATE())";
            Integer exists = jdbcTemplate.queryForObject(checkSql, Integer.class, mallBUsers.getPhone());
            BigDecimal weightCount = new BigDecimal(mallBUsers.getWeightCount());
            BigDecimal todayWeight = weightCount.subtract(new BigDecimal(lastDayWeightCount(mallBUsers.getPhone())));
            if (exists > 0) {
                // 如果当天已有数据，则更新
                String updateSql = "UPDATE mall_b_users_count SET quantify = 0, Weight = ?, weight_count = ?, quantify_count = 0 " +
                        "WHERE phone = ? AND DATE(update_time) = CURDATE()";
                jdbcTemplate.update(updateSql, todayWeight, weightCount, mallBUsers.getPhone());
            } else {
                // 如果当天没有数据，则插入新记录
                String sql = "INSERT INTO mall_b_users_count(phone,quantify,Weight,weight_count,quantify_count,update_time) " +
                        "VALUES(?,0,?,?,0,CURDATE())";
                jdbcTemplate.update(sql, mallBUsers.getPhone(), todayWeight, weightCount);
            }
            // 插入数据 到 量化值 表
            if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM quantization_value WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, mallBUsers.getPhone()) == 0) {
                String sql = "Insert into quantization_value(phone,value,total_value,credit_value,total_credit_Value,count_result,platform_gold,total_platform_gold,update_date)values(?,0,0,0,0,0,0,0,CURDATE())";
                jdbcTemplate.update(sql, mallBUsers.getPhone());
            }
            // 插入数据 到 核销数据 (write_off_data) 表
            if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM write_off_data WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, mallBUsers.getPhone()) == 0) {
                String sql = "Insert into write_off_data(phone,write_off_subsidy,write_off_subsidy_total,un_write_off_subsidy,promotion_used,total_promotion_used,write_off_gold,update_date)values(?,0,0,0,0,0,0,CURDATE())";
                jdbcTemplate.update(sql, mallBUsers.getPhone());
            }

            if(mallBUsers.getUserType().equals("B") || mallBUsers.getUserType().equals("CB")){
                // 插入数据 到 量化值进化 (quantify_evolve) 表
                if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM quantify_evolve WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, mallBUsers.getPhone()) == 0) {
                    String sql = "Insert into quantify_evolve(phone,quantify_evolve,quantify_evolve_total,update_date)values(?,0,0,CURDATE())";
                    jdbcTemplate.update(sql, mallBUsers.getPhone());
                }
                // 插入数据 到 量化进化 (credit_evolve) 表
                if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM credit_evolve WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, mallBUsers.getPhone()) == 0) {
                    String sql = "Insert into credit_evolve(phone,credit_evolve,credit_evolve_total,update_date)values(?,0,0,CURDATE())";
                    jdbcTemplate.update(sql, mallBUsers.getPhone());
                }
            }else{
                // 插入数据 到 C量化值进化 (cquantify_evolve) 表
                if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM cquantify_evolve WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, mallBUsers.getPhone()) == 0) {
                    String sql = "Insert into cquantify_evolve(phone,quantify_evolve,quantify_evolve_total,update_date)values(?,0,0,CURDATE())";
                    jdbcTemplate.update(sql, mallBUsers.getPhone());
                }
                // 插入数据 到 C量化进化 (ccredit_evolve) 表
                if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM ccredit_evolve WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, mallBUsers.getPhone()) == 0) {
                    String sql = "Insert into ccredit_evolve(phone,credit_evolve,credit_evolve_total,update_date)values(?,0,0,CURDATE())";
                    jdbcTemplate.update(sql, mallBUsers.getPhone());
                }
            }
        } catch (Exception e) {
            throw new BusinessException("保存mallB用户数据失败: " + e.getMessage());
        }
    }

    // 保存mallB系统的超级管理员手机号码
    public void saveSuperAdminPhone(String superAdminPhone) {
        // 插入数据 到 关系链(mall_b_users) 表
        String sql = "INSERT INTO mall_b_users(id, username, parent_id,parent_username,user_type, chain_type, status, deduction_money_limit, business_name, phone, fans, address, town_code, jurisdiction, flag, login_address,business_license,create_time) VALUES (1, 'admin', '', '', 'admin', '', 0, 0, '', ?, 0, '','', 0, 0, '', '', NOW())";
        jdbcTemplate.update(sql, superAdminPhone);
        // 插入数据 到 量化数 (mall_b_users_count) 表
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM mall_b_users_count WHERE phone = ? AND DATE(update_time) = CURDATE() )", Integer.class, superAdminPhone) == 0) {
            sql = "INSERT INTO mall_b_users_count(phone,quantify,Weight,weight_count,quantify_count,update_time) VALUES(?,0,0,0,0,CURDATE())";
            jdbcTemplate.update(sql, superAdminPhone);
        }
        // 插入数据 到 量化值 表
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM quantization_value WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, superAdminPhone) == 0) {
            sql = "Insert into quantization_value(phone,value,total_value,credit_value,total_credit_Value,count_result,platform_gold,total_platform_gold,update_date)values(?,0,0,0,0,0,0,0,CURDATE())";
            jdbcTemplate.update(sql, superAdminPhone);
        }
        // 插入数据 到 核销数据 (write_off_data) 表
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM write_off_data WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, superAdminPhone) == 0) {
            sql = "Insert into write_off_data(phone,write_off_subsidy,write_off_subsidy_total,un_write_off_subsidy,promotion_used,total_promotion_used,write_off_gold,update_date)values(?,0,0,0,0,0,0,CURDATE())";
            jdbcTemplate.update(sql, superAdminPhone);
        }
        // 插入数据 到 量化值进化 (quantify_evolve) 表
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM quantify_evolve WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, superAdminPhone) == 0) {
            sql = "Insert into quantify_evolve(phone,quantify_evolve,quantify_evolve_total,update_date)values(?,0,0,CURDATE())";
            jdbcTemplate.update(sql, superAdminPhone);
        }
        // 插入数据 到 量化进化 (credit_evolve) 表
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM credit_evolve WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, superAdminPhone) == 0) {
            sql = "Insert into credit_evolve(phone,credit_evolve,credit_evolve_total,update_date)values(?,0,0,CURDATE())";
            jdbcTemplate.update(sql, superAdminPhone);
        }
    }
    //查询 用户禁用状态记录表(disable_status) status = 1 的用户
    public List<Map<String, Object>> queryDisableStatus() {
        String sql = "SELECT user_id FROM disable_status WHERE status = 1";
        return jdbcTemplate.queryForList(sql);
    }
    //更新用户状态
    @Transactional
    public int updateMallBUsersStatus(String id, String status) {
        String sql = "UPDATE mall_b_users SET status = ? WHERE id = ?";
        return jdbcTemplate.update(sql, status, id);
    }
    // 当状态为1时，把禁用的用户插入到 用户禁用状态记录表(disable_status)
    public void insertDisableStatus(String userId, String status) {
        String sql = "INSERT INTO disable_status(user_id,status, update_time) VALUES (? ,?, NOW())";
        jdbcTemplate.update(sql, userId, status);
    }
    // 当状态不为1时，把禁用的用户从 用户禁用状态记录表(disable_status) 删除
    public void deleteDisableStatus(String userId) {
        String sql = "DELETE FROM disable_status WHERE user_id = ?";
        jdbcTemplate.update(sql, userId);
    }

    /**
     *  获取历史到昨日的总分量
     */
    public String lastDayWeightCount(String phone) {
        try{
            String sql = "SELECT weight_count FROM mall_b_users_count WHERE phone = ? and update_time = CURDATE() - INTERVAL 1 DAY";
            String sum = jdbcTemplate.queryForObject(sql, String.class, phone);
            return sum == null ? "0" : sum;
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     *  获取用户状态
     */
    public List<Map<String, Object>> getUsersStatus() {
        String sql = "SELECT id,status_name FROM user_status WHERE is_enabled = 1 ORDER BY id ASC";
        return jdbcTemplate.queryForList(sql);
    }

    /**
     *  获取用户类型
     */
    public List<Map<String, Object>> getUserTypes() {
        String sql = "SELECT DISTINCT user_type from mall_b_users";
        return jdbcTemplate.queryForList(sql);
    }
}
